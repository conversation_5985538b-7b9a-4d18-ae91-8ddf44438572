/**
 * 智能体功能全局样式
 */

// 智能体布局相关样式
.frame-layout-wrapper {
  &.ai-agent-active {
    // 当智能体激活时的样式调整
    .layout {
      transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    // 确保侧边栏在智能体激活时的正确显示
    .frame-aside {
      z-index: 997;
    }
    
    // 头部导航栏的调整
    .frame-header-wrap {
      transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
}

// 智能体面板全局样式
.ai-agent-panel {
  // 确保在所有主题下都有正确的样式
  @include themify() {
    background-color: themed("$componentBg");
    border-left: 1px solid themed("$--border-color-base");
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  }
  
  // 滚动条样式
  ::-webkit-scrollbar {
    width: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @include themify() {
      background: themed("$--background-color-base");
    }
  }
  
  ::-webkit-scrollbar-thumb {
    @include themify() {
      background: themed("$--border-color-base");
      border-radius: 3px;
    }
    
    &:hover {
      @include themify() {
        background: themed("$--color-text-regular");
      }
    }
  }
  
  // 移动端适配
  @media (max-width: 768px) {
    width: 100% !important;
    z-index: 1001 !important;
    
    // 添加遮罩层效果
    &::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: -1;
    }
  }
  
  // 平板适配
  @media (min-width: 769px) and (max-width: 1024px) {
    width: 40% !important;
  }
}

// 智能体切换按钮全局样式
.ai-agent-toggle-button {
  // 确保按钮在所有情况下都可见
  z-index: 1000;
  
  // 避免与其他悬浮元素冲突
  .toggle-btn {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
    
    // 确保在暗色主题下也有良好的对比度
    @include themify() {
      @if themed("$light-theme") == 0 {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2);
      }
    }
  }
  
  // 当页面滚动时保持固定位置
  position: fixed !important;
  
  // 在全屏模式下隐藏
  .layout-fullscreen & {
    display: none;
  }
}

// 智能体容器内部样式优化
.ai-agent-container {
  // 确保内容区域正确填充
  .ai-agent-content {
    // iframe 样式优化
    .ai-agent-iframe {
      background-color: #fff;
      
      // 加载时的占位样式
      &:not([src]) {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading-shimmer 1.5s infinite;
      }
    }
    
    // 占位内容样式优化
    .ai-agent-placeholder {
      .placeholder-content {
        // 渐变背景
        background: linear-gradient(135deg, 
          rgba(102, 126, 234, 0.1) 0%, 
          rgba(118, 75, 162, 0.1) 100%);
        border-radius: 12px;
        padding: 40px 20px;
        
        // 功能列表样式
        .feature-list {
          .feature-item {
            padding: 8px 16px;
            margin: 8px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
            
            @include themify() {
              background-color: rgba(themed("$--color-primary"), 0.05);
              border: 1px solid rgba(themed("$--color-primary"), 0.1);
            }
            
            &:hover {
              @include themify() {
                background-color: rgba(themed("$--color-primary"), 0.1);
                transform: translateY(-1px);
              }
            }
          }
        }
      }
    }
    
    // 错误状态样式
    .ai-agent-error {
      .error-icon {
        animation: error-pulse 2s infinite;
      }
    }
    
    // 加载状态样式
    .ai-agent-loading {
      .el-loading-mask {
        background-color: rgba(255, 255, 255, 0.9);
      }
    }
  }
  
  // 头部样式优化
  .ai-agent-header {
    backdrop-filter: blur(10px);
    
    @include themify() {
      background-color: rgba(themed("$componentBg"), 0.95);
      border-bottom: 1px solid rgba(themed("$--border-color-base"), 0.5);
    }
    
    .ai-agent-title {
      i {
        animation: icon-rotate 3s linear infinite;
      }
    }
  }
}

// 动画定义
@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes error-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes icon-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式断点
$mobile-breakpoint: 768px;
$tablet-breakpoint: 1024px;
$desktop-breakpoint: 1200px;

// 响应式布局调整
@media (max-width: $mobile-breakpoint) {
  .frame-layout-wrapper {
    &.ai-agent-active {
      .layout {
        width: 0 !important;
        overflow: hidden;
      }
    }
  }
}

@media (min-width: $mobile-breakpoint + 1px) and (max-width: $tablet-breakpoint) {
  .frame-layout-wrapper {
    &.ai-agent-active {
      .layout {
        width: 60% !important;
      }
    }
  }
  
  .ai-agent-panel {
    width: 40% !important;
  }
}

@media (min-width: $desktop-breakpoint) {
  .frame-layout-wrapper {
    &.ai-agent-active {
      .layout {
        width: 70% !important;
      }
    }
  }
  
  .ai-agent-panel {
    width: 30% !important;
  }
}

// 打印样式
@media print {
  .ai-agent-panel,
  .ai-agent-toggle-button {
    display: none !important;
  }
  
  .frame-layout-wrapper.ai-agent-active .layout {
    width: 100% !important;
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .ai-agent-panel {
    border-left-width: 2px;
  }
  
  .ai-agent-toggle-button .toggle-btn {
    border: 2px solid currentColor;
  }
}

// 减少动画模式支持
@media (prefers-reduced-motion: reduce) {
  .frame-layout-wrapper,
  .ai-agent-panel,
  .ai-agent-toggle-button .toggle-btn,
  .ai-agent-container * {
    transition: none !important;
    animation: none !important;
  }
}
