/**
 * 智能体状态管理模块
 */

import {
  checkAiAgentAvailability as apiCheckAvailability,
  getAiAgentConfig as apiGetConfig,
  getAiAgentUrl as apiGetUrl,
  mockApiCall
} from '@/api/aiAgent';

const state = {
  // 智能体功能是否可用
  isAiAgentAvailable: false,
  
  // 是否显示智能体区域
  showAiAgent: false,
  
  // 智能体加载状态
  aiAgentLoading: false,
  
  // 智能体错误状态
  aiAgentError: false,
  
  // 智能体页面URL
  aiAgentUrl: '',
  
  // 智能体配置信息
  aiAgentConfig: null,
  
  // 用户偏好设置
  userPreferences: {
    autoShow: false, // 是否自动显示
    defaultWidth: 33.33, // 默认宽度百分比
    rememberState: true, // 是否记住显示状态
  },
  
  // 当前路由信息
  currentRoute: null,
  
  // 支持智能体的专业类型列表
  supportedProfessions: [31], // 31为核心网专业
};

const mutations = {
  // 设置智能体可用性
  SET_AI_AGENT_AVAILABLE(state, available) {
    state.isAiAgentAvailable = available;
  },
  
  // 设置显示状态
  SET_SHOW_AI_AGENT(state, show) {
    state.showAiAgent = show;
    
    // 如果启用了记住状态，保存到localStorage
    if (state.userPreferences.rememberState) {
      localStorage.setItem('aiAgent_showState', JSON.stringify(show));
    }
  },
  
  // 设置加载状态
  SET_AI_AGENT_LOADING(state, loading) {
    state.aiAgentLoading = loading;
  },
  
  // 设置错误状态
  SET_AI_AGENT_ERROR(state, error) {
    state.aiAgentError = error;
  },
  
  // 设置智能体URL
  SET_AI_AGENT_URL(state, url) {
    state.aiAgentUrl = url;
  },
  
  // 设置智能体配置
  SET_AI_AGENT_CONFIG(state, config) {
    state.aiAgentConfig = config;
  },
  
  // 更新用户偏好
  UPDATE_USER_PREFERENCES(state, preferences) {
    state.userPreferences = { ...state.userPreferences, ...preferences };
    localStorage.setItem('aiAgent_preferences', JSON.stringify(state.userPreferences));
  },
  
  // 设置当前路由
  SET_CURRENT_ROUTE(state, route) {
    state.currentRoute = route;
  },
  
  // 更新支持的专业类型
  UPDATE_SUPPORTED_PROFESSIONS(state, professions) {
    state.supportedProfessions = professions;
  },
};

const actions = {
  // 初始化智能体模块
  async initAiAgent({ commit, dispatch }, route) {
    commit('SET_CURRENT_ROUTE', route);
    
    // 恢复用户偏好设置
    const savedPreferences = localStorage.getItem('aiAgent_preferences');
    if (savedPreferences) {
      try {
        const preferences = JSON.parse(savedPreferences);
        commit('UPDATE_USER_PREFERENCES', preferences);
      } catch (error) {
        console.warn('Failed to parse AI agent preferences:', error);
      }
    }
    
    // 检查智能体可用性
    await dispatch('checkAiAgentAvailability', route);
    
    // 恢复显示状态
    const savedShowState = localStorage.getItem('aiAgent_showState');
    if (savedShowState && state.userPreferences.rememberState) {
      try {
        const showState = JSON.parse(savedShowState);
        if (showState && state.isAiAgentAvailable) {
          commit('SET_SHOW_AI_AGENT', true);
          await dispatch('loadAiAgent');
        }
      } catch (error) {
        console.warn('Failed to parse AI agent show state:', error);
      }
    }
  },
  
  // 检查智能体功能可用性
  async checkAiAgentAvailability({ commit, state }, route) {
    try {
      // 检查当前页面是否支持智能体功能
      const isSupported = await checkPageSupport(route);
      
      if (isSupported) {
        // 检查专业类型是否支持
        const professionSupported = await checkProfessionSupport(route);
        commit('SET_AI_AGENT_AVAILABLE', professionSupported);
      } else {
        commit('SET_AI_AGENT_AVAILABLE', false);
      }
    } catch (error) {
      console.error('Failed to check AI agent availability:', error);
      commit('SET_AI_AGENT_AVAILABLE', false);
    }
  },
  
  // 切换智能体显示状态
  async toggleAiAgent({ commit, state, dispatch }) {
    if (!state.isAiAgentAvailable) {
      return;
    }
    
    const newShowState = !state.showAiAgent;
    
    if (newShowState && !state.aiAgentUrl) {
      // 如果要显示但还没有URL，先加载
      await dispatch('loadAiAgent');
    }
    
    commit('SET_SHOW_AI_AGENT', newShowState);
  },
  
  // 显示智能体
  async showAiAgent({ commit, state, dispatch }) {
    if (!state.isAiAgentAvailable) {
      return;
    }
    
    if (!state.aiAgentUrl) {
      await dispatch('loadAiAgent');
    }
    
    commit('SET_SHOW_AI_AGENT', true);
  },
  
  // 隐藏智能体
  hideAiAgent({ commit }) {
    commit('SET_SHOW_AI_AGENT', false);
  },
  
  // 加载智能体
  async loadAiAgent({ commit, state }) {
    if (state.aiAgentLoading) {
      return;
    }
    
    commit('SET_AI_AGENT_LOADING', true);
    commit('SET_AI_AGENT_ERROR', false);
    
    try {
      // 这里可以调用API获取智能体URL和配置
      const result = await loadAiAgentConfig(state.currentRoute);
      
      commit('SET_AI_AGENT_URL', result.url);
      commit('SET_AI_AGENT_CONFIG', result.config);
    } catch (error) {
      console.error('Failed to load AI agent:', error);
      commit('SET_AI_AGENT_ERROR', true);
    } finally {
      commit('SET_AI_AGENT_LOADING', false);
    }
  },
  
  // 重新加载智能体
  async reloadAiAgent({ commit, dispatch }) {
    commit('SET_AI_AGENT_URL', '');
    commit('SET_AI_AGENT_CONFIG', null);
    await dispatch('loadAiAgent');
  },
  
  // 更新用户偏好
  updatePreferences({ commit }, preferences) {
    commit('UPDATE_USER_PREFERENCES', preferences);
  },
  
  // 清理智能体状态
  clearAiAgent({ commit }) {
    commit('SET_SHOW_AI_AGENT', false);
    commit('SET_AI_AGENT_URL', '');
    commit('SET_AI_AGENT_CONFIG', null);
    commit('SET_AI_AGENT_LOADING', false);
    commit('SET_AI_AGENT_ERROR', false);
  },
};

const getters = {
  // 智能体是否可用
  isAiAgentAvailable: state => state.isAiAgentAvailable,
  
  // 是否显示智能体
  showAiAgent: state => state.showAiAgent,
  
  // 智能体加载状态
  aiAgentLoading: state => state.aiAgentLoading,
  
  // 智能体错误状态
  aiAgentError: state => state.aiAgentError,
  
  // 智能体URL
  aiAgentUrl: state => state.aiAgentUrl,
  
  // 智能体配置
  aiAgentConfig: state => state.aiAgentConfig,
  
  // 用户偏好
  userPreferences: state => state.userPreferences,
  
  // 当前路由是否支持智能体
  currentRouteSupported: state => {
    return state.isAiAgentAvailable && state.currentRoute;
  },
};

// 辅助函数

// 检查页面是否支持智能体功能
async function checkPageSupport(route) {
  // 目前主要支持工单详情页面
  const supportedPages = [
    'common_orderDetail',
    'CommonWoDetail',
    // 可以添加更多支持的页面
  ];

  return supportedPages.includes(route.name) ||
         route.path.includes('orderDetail') ||
         route.path.includes('WoDetail');
}

// 检查专业类型是否支持
async function checkProfessionSupport(route) {
  try {
    // 在开发阶段使用模拟API
    const isDev = process.env.NODE_ENV === 'development';

    if (isDev) {
      // 临时开启所有专业类型支持，便于测试
      return true;
    }

    // 生产环境调用真实API
    const params = {
      woId: route.query.woId || route.params.woId,
      professionId: route.query.professionId || route.params.professionId,
      routeName: route.name
    };

    const response = await apiCheckAvailability(params);
    return response.success && response.data.available;
  } catch (error) {
    console.error('Failed to check profession support:', error);
    return false;
  }
}

// 加载智能体配置
async function loadAiAgentConfig(route) {
  try {
    const isDev = process.env.NODE_ENV === 'development';

    if (isDev) {
      // 开发环境使用模拟数据
      const mockResponse = await mockApiCall('getConfig');
      const mockUrlResponse = await mockApiCall('getUrl');

      return {
        url: mockUrlResponse.data.url,
        config: mockResponse.data,
      };
    }

    // 生产环境调用真实API
    const params = {
      woId: route.query.woId || route.params.woId,
      professionId: route.query.professionId || route.params.professionId,
    };

    const [configResponse, urlResponse] = await Promise.all([
      apiGetConfig(params),
      apiGetUrl(params)
    ]);

    return {
      url: urlResponse.success ? urlResponse.data.url : '',
      config: configResponse.success ? configResponse.data : null,
    };
  } catch (error) {
    console.error('Failed to load AI agent config:', error);
    throw error;
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
