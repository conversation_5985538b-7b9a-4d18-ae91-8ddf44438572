<template>
  <div>
    <!-- v-show="!props.markTabChange" -->
    <div v-if="!props.markTabChange" class="flex-bc">
      <el-page-header @back="jumpTo('vulnerabilityHome')" class="mb-2">
        <template #content>
          <span class="mr-3 font-bold">
            漏洞详情 ({{ props.eventInfo["ip"] }})
          </span>
        </template>
      </el-page-header>
      <div style="font-size: 14px" class="mb-1 mr-3">
        切换资产信息：<el-select
          v-model="assetIp"
          placeholder="选择IP"
          style="width: 240px"
        >
          <el-option
            v-for="item in assetAllIp"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </div>
    </div>
    <div class="ml-2 mr-2 mt-5">
      <im-table
        show-overflow-tooltip
        :data="tableData"
        center
        toolbar
        :table-alert="{
          closable: false
        }"
        :operator="{
          label: '操作',
          width: 160
        }"
        :height="tableOption.height"
        :stripe="tableOption.stripe"
        show-checkbox
        :column-storage="createColumnStorage('vul-detail', 'remote')"
        :pagination="tablePage"
        :loading="tableLoading"
        @on-reload="resetTablePageAndQuery"
        @selection-change="selectionChangeHandler"
        @on-page-change="queryEventData"
      >
        <!-- 表格左侧菜单 -->
        <template #toolbar-left="{ size }">
          <div class="flex-sc pt-0.5">
            <!-- 分段选择器，用于选择事件处理状态 -->
            <el-segmented
              v-model="searchCondition['dealWith']"
              :options="segmentData"
              @change="segmentChangeHandler"
            ></el-segmented>
          </div>
        </template>

        <!-- 表格右侧菜单 -->
        <template #toolbar-right="{ size }">
          <div class="float-left flex-sc pr-3 pt-0.5 gap-3">
            <el-segmented
              v-model="state.dateRangeSign"
              :options="state.timeSegmentOptions"
              @change="
                () => {
                  dateTimeRange = [];
                  resetTablePageAndQuery();
                }
              "
            >
            </el-segmented>
            <!-- 日期选择器，用于选择事件时间范围 -->
            <el-date-picker
              clearable
              v-model="dateTimeRange"
              type="daterange"
              range-separator="到"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 200px"
              @change="
                () => {
                  state.dateRangeSign = null;
                  resetTablePageAndQuery();
                }
              "
            />

            <!-- 风险级别选择器，用于选择事件风险级别 -->
            <el-select
              v-model="searchCondition['vullevel']"
              placeholder="风险级别"
              multiple
              collapse-tags
              clearable
              style="width: 150px"
              @change="riskLevelChangeHandler"
            >
              <el-option
                v-for="item in riskLevelData02"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              />
            </el-select>

            <!-- vulName 漏洞名称 -->
            <el-input
              v-if="!props.markTabChange"
              clearable
              @change="queryEventData"
              v-model="vulName"
              style="width: 150px"
              size="small"
              placeholder="漏洞名称"
              :suffix-icon="Search"
            />

            <!-- 更多操作 -->
            <el-dropdown style="margin: 0 10px">
              <el-button type="primary"> 批量操作 </el-button>
              <template #dropdown>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    :disabled="
                      state.selectedEventRows.length == 0 ||
                      (item == '批量派单' &&
                        state.searchCondition.dealWith != '1')
                    "
                    style="padding: 0.4rem 1rem"
                    v-for="(item, index) in batchOperation"
                    :key="index"
                    @click.native="
                      item == '批量派单'
                        ? handleCommand(item, '批量派单')
                        : handleCommand(item, 'moreFunction')
                    "
                    >{{ item }}
                  </el-dropdown-item>
                  <!-- <el-dropdown-item :disabled="(state.selectedEventRows.length == 0)"
                                        style="padding: 0.4rem 1rem;" @click.native="handleCommand('批量验证', '批量验证')">
                                        批量验证
                                    </el-dropdown-item> -->
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 导入 -->
            <el-dropdown @command="handleCommand" placement="bottom">
              <el-button
                >导入<svg
                  style="
                    width: 17px;
                    height: 16px;
                    margin-bottom: 2px;
                    margin-left: 2px;
                    color: #666;
                  "
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 1024 1024"
                >
                  <path
                    fill="currentColor"
                    d="M544 864V672h128L512 480 352 672h128v192H320v-1.6c-5.376.32-10.496 1.6-16 1.6A240 240 0 0 1 64 624c0-123.136 93.12-223.488 212.608-237.248A239.808 239.808 0 0 1 512 192a239.872 239.872 0 0 1 235.456 194.752c119.488 13.76 212.48 114.112 212.48 237.248a240 240 0 0 1-240 240c-5.376 0-10.56-1.28-16-1.6v1.6z"
                  ></path>
                </svg>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    style="padding: 0.4rem 1rem"
                    v-for="item in importTemplate"
                    :key="item['type']"
                    :command="item['type']"
                    >{{ item["name"] }}</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 导出事件数据 -->
            <el-tooltip content="导出数据" placement="top" :open-delay="1000">
              <el-button
                :icon="useRenderIcon('EP-Download')"
                circle
                :size="size"
                :disabled="tableData.length == 0"
                @click="exportEventHandler"
              />
            </el-tooltip>
          </div>
        </template>
        <!-- 表格操作按钮 -->
        <template #operator="{ row, size, type }">
          <!-- 详情按钮 -->
          <el-button
            v-if="row['status'] != '2' && row['dispatch_status'] == '未派单'"
            :size="size"
            :type="type"
            text
            :icon="useRenderIcon('EP-View')"
            @click="handleCommand(row, 'moreFunction', 'one')"
          >
            处置
          </el-button>
          <el-button
            :size="size"
            :type="type"
            text
            :icon="useRenderIcon('EP-View')"
            @click="detailViewHandler(row)"
          >
            详情
          </el-button>
        </template>

        <!-- <template #vulName="{ row }">
                    <el-tooltip effect="dark" placement="top">
                        <template #content>
                            <div style="max-width: 666px;">{{ row['vulName'] }}</div>
                        </template>
                        <span style="width: 170px;
                            display: inline-block;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            ">
                            {{ row['vulName'] }}
                        </span>
                    </el-tooltip>
                </template> -->

        <!-- <template #deviceName="{ row }">
                    <el-tooltip effect="dark" placement="top">
                        <template #content>
                            <div style="max-width: 666px;">{{ row['deviceName'] }}</div>
                        </template>
                        <span style="width: 170px;
                            display: inline-block;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            ">
                            {{ row['deviceName'] }}
                        </span>
                    </el-tooltip>
                </template> -->
        <!--   work_num  refOrgNameTree-->
        <!-- <template #assetTypeName="{ row }">
                    <el-tooltip effect="dark" placement="top">
                        <template #content>
                            <div style="max-width: 666px;">{{ row['assetTypeName'] }}</div>
                        </template>
                        <span style="width: 170px;
                            display: inline-block;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            ">
                            {{ row['assetTypeName'] }}
                        </span>
                    </el-tooltip>
                </template> -->

        <!-- 风险级别列 -->
        <template #vullevel="{ row }">
          <!-- <div>{{ row.vullevel + '' }}</div> -->
          <el-tag
            :color="getRiskLevelColor(row.vullevel + '')"
            class="text-white border-none"
          >
            {{ getRiskLevelLabel03(row.vullevel + "") }}
          </el-tag>
        </template>
        <!-- 事件处理状态列 -->
        <template #status="{ row }">
          <el-tag effect="light" :type="getDealStatusType(row.status + '')">
            {{ getSegmentLabel(row.status + "") }}
          </el-tag>
        </template>

        <!-- <template #vulsuggest="{ row }">
                    <el-tooltip effect="dark" placement="top">
                        <template #content>
                            <div style="max-width: 666px;">{{ row['vulsuggest'] }}</div>
                        </template>
                        <span style="width: 233px;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            display: inline-block;">
                            {{ row['vulsuggest'] }}
                        </span>
                    </el-tooltip>
                </template> -->

        <template #dispatch_status="{ row }">
          <div>
            <el-tag
              v-if="row.dispatch_status == '已派单'"
              class="tag"
              size="small"
              type="success"
              >已派单</el-tag
            >
            <el-tag v-else class="tag" size="small" type="danger"
              >未派单</el-tag
            >
          </div>
        </template>
      </im-table>
    </div>

    <el-drawer
      title="漏洞详情"
      size="55%"
      destroy-on-close
      append-to-body
      v-model="showInfo"
    >
      <SecurityVulMonitorDetailDrawer
        v-if="showInfo"
        :vulRecord="currentInfoRow"
      />
    </el-drawer>

    <el-drawer
      title="导入"
      size="45%"
      destroy-on-close
      append-to-body
      v-model="showImport"
    >
      <SecurityVulImportDrawer
        v-if="showImport"
        @refresh="queryEventData"
        :template-type="commandImportType"
      />
    </el-drawer>

    <el-dialog
      title="批量处置"
      width="33%"
      destroy-on-close
      append-to-body
      v-model="showHandle"
    >
      <vulnerabilityDetailsMoreOperations
        v-if="showHandle"
        @closeDraw="closeDraw"
        :batchOperationModelData="batchOperationRowsData"
      />
    </el-dialog>

    <EventDispatchModal
      @success="queryEventData"
      :form="eventDispatchModalForm"
      :modelValue="eventDispatchModalVisable"
      @update:modelValue="eventDispatchModalVisable = false"
    />
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  reactive,
  ref,
  toRefs,
  watch
} from "vue";
import dayjs from "dayjs";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElTree } from "element-plus";
import SecurityVulMonitorDetailDrawer from "@/views/modules/security/securityVulnerability/components/SecurityVulMonitorDetailDrawer.vue";
import SecurityVulImportDrawer from "@/views/modules/security/securityVulnerability/components/SecurityVulImportDrawer.vue";
import vulnerabilityDetailsMoreOperations from "@/views/modules/security/securityVulnerability/components/vulnerabilityDetailsMoreOperations.vue";
import EventDispatchModal from "@/views/modules/security/securityVulnerability/components/EventDispatchModal.vue";

import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import {
  getDealStatusType,
  getRiskLevelColor,
  getRiskLevelLabel,
  getSegmentLabel,
  getDealStatusType02,
  getSegmentLabel02,
  riskLevelData,
  segmentData,
  getRiskLevelColor02,
  getRiskLevelLabel02,
  riskLevelData02,
  getRiskLevelLabel03
} from "@/views/modules/security/securityVulnerability/util/vulnerability_data";

import {
  vulDetailByAssetMethod,
  importVulDetailByAssetMethod,
  templateTypesMethod,
  getAllIp,
  bulkOrder,
  batchVerification
} from "@/views/modules/security/securityVulnerability/api/vulnerabilityAssetTableInfoInterface";
import { Search } from "@element-plus/icons-vue";
import { createColumnStorage } from "@/components/ItsmCommon";
import ImTable from "@/components/ItsmCommon/table/ImTable.vue";

const { $router, $confirm, $message } =
  getCurrentInstance().appContext.config.globalProperties;

const deptTree = ref<InstanceType<typeof ElTree>>();
const appTreeRef = ref<InstanceType<typeof ElTree>>();

//数据对象
const state = reactive({
  checkAll: true,
  totalTagCount: 0,
  activeTabName: "deptView",
  tableLoading: false,
  deptKeyWord: null,
  // deptData: firstLevelDeptData,
  deptData: [],
  appKeyWord: null,
  appData: [],
  eventTagData: [],
  columnCondition: {
    value: null,
    field: "assetName",
    fuzzyable: true,
    operator: "fuzzy"
  },
  dateRangeSign: "30d",
  timeSegmentOptions: [
    {
      label: "全部",
      value: ""
    },
    {
      label: "近24小时",
      value: "1d"
    },
    {
      label: "近7天",
      value: "7d"
    },
    {
      label: "近30天",
      value: "30d"
    }
  ],
  dateTimeRange: [],
  searchCondition: {
    // orgId: "",
    // asset_app_name: "",
    dealWith: "1"
    // dealStatus: null,
    // vullevel: "",
    // event_type_tag: null,
    // model: "event"
  },
  // "conditions": [
  //   { "field": "assetName", "fuzzyable": true, "operator": "fuzzy", "value": "name" },
  //   { "field": "ip", "fuzzyable": true, "operator": "fuzzy", "value": "ip" },
  //   { "field": "refOrgNameTree", "fuzzyable": false, "value": "zXCu-EN-Pn_2CISvf9V" },
  //   { "field": "asset_type_name", "fuzzyable": false, "value": 259 },
  //   { "field": "refsys", "fuzzyable": true, "operator": "fuzzy", "value": "system" },
  //   { "field": "vullevel", "fuzzyable": false, "value": "4,3,2" },
  //   { "field": "dealStatus", "fuzzyable": false, "value": "1" }
  // ]
  columnSearchOptions: [
    {
      label: "资产名称",
      value: "assetName"
    },
    {
      label: "资产IP",
      value: "ip"
    },
    {
      label: "业务系统",
      value: "refsys"
    }
  ],
  tableData: [],
  // 资产信息(以ip为key)
  assetByIpMap: {},
  tablePage: {
    align: "right",
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  deal: {
    visible: false,
    title: "",
    unitIds: [],
    defaultAction: null
  },
  selectedEvents: [] as Array<string>,
  selectedEventRows: [],
  ipBlock: {
    visible: false,
    ipAddress: "",
    defaultPlugLabel: "安全事件"
  },
  dsSelData: [
    {
      value: 1,
      label: "未修复"
    },
    {
      value: 2,
      label: "已修复"
    }
  ]
});
const {
  activeTabName,
  tableLoading,
  deptKeyWord,
  deptData,
  appKeyWord,
  appData,
  eventTagData,
  columnCondition,
  dateTimeRange,
  searchCondition,
  columnSearchOptions,
  tableData,
  tablePage,
  deal,
  selectedEvents,
  ipBlock
} = toRefs(state);

const vulName = ref("");
const vulsuggestToolTipRef = ref(null);

const batchOperation = ref(["批量派单", "批量处置"]);

watch(deptKeyWord, val => {
  deptTree.value!.filter(val);
});

watch(appKeyWord, val => {
  appTreeRef.value!.filter(val);
});

//定义事件
const emit = defineEmits(["jump-to", "event-select", "searchQuery"]);

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return props.markTabChange
    ? document.documentElement.offsetHeight - 420
    : document.documentElement.offsetHeight - 300;
});

const tableOption = reactive({
  headerAlign: "center",
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 130,
  height: tableHeight,
  rowKey: "vul_id",
  column: [],
  showOverflowTooltip: true
});

const props = defineProps({
  eventInfo: Object,
  markTabChange: Boolean,
  triggerVulViewTable: Object,
  needsPassedData: Object
});
// console.log(props.eventInfo);

console.log("props.needsPassedData", props.needsPassedData);
watch(
  props.needsPassedData,
  val => {
    console.log("props.needsPassedData", val);
    if (val["timeSelect"] == "") {
      state.dateRangeSign = "";
    } else if (val["timeSelect"] != "30d") {
      state.dateRangeSign = val["timeSelect"];
    }
    if (val?.["dealStatus"]) {
      console.log(
        "searchCondition['dealWith']",
        state.searchCondition["dealWith"]
      );
      // (+state.searchCondition['dealWith'] == 1 ? "undisposed" : (+state.searchCondition['dealWith'] == 2 ? "disposalof" : "noNeedHandle"))
      switch (val?.["dealStatus"]) {
        case "undisposed":
          state.searchCondition["dealWith"] = "1";
          break;
        case "disposalof":
          state.searchCondition["dealWith"] = "2";
          break;
        case "noNeedHandle":
          state.searchCondition["dealWith"] = "0";
        default:
          break;
      }
    }
    state.searchCondition["vullevel"] = val?.["reseverity"] || [];
  },
  { deep: true, immediate: true }
);

//重置分页后查询事件数据
const resetTablePageAndQuery = () => {
  state.tablePage.currentPage = 1;
  queryEventData();
};

const tmpQuery = ref(null);
watch(props.triggerVulViewTable, val => {
  if (Object.keys(props.triggerVulViewTable.query).length == 0) {
    console.log("对象为空");
    tmpQuery.value = null;
  } else {
    console.log("对象不为空");
    tmpQuery.value = props.triggerVulViewTable.query;
  }
  resetTablePageAndQuery();
});

/**
 * 日期范围(标签和列表能复用)
 */
const computedDateRange = computed(() => {
  let dateRange;
  if (state.dateRangeSign) {
    dateRange = state.dateRangeSign;
  } else {
    if (state.dateTimeRange?.length == 2) {
      dateRange = [
        dayjs(state.dateTimeRange[0]).format("YYYY-MM-DD HH:mm:ss"),
        dayjs(state.dateTimeRange[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
      ];
    } else {
      dateRange = null;
    }
  }
  return dateRange;
});

const assetAllIp = ref([]);
const assetIp = ref("");
assetIp.value = props.eventInfo["ip"];
watch(assetIp, val => {
  console.log(val);
  queryEventData();
});

const getAllAssetIp = () => {
  getAllIp({
    //查询条件
    conditions: [],
    //日期范围
    dateRange: "all",
    //当前页码
    pageNum: "",
    //每页显示条数
    pageSize: ""
  }).then(res => {
    assetAllIp.value = res["data"];
  });
};
getAllAssetIp();

const searchTmpData = tmpConditions => {
  if (
    state.searchCondition["vullevel"] &&
    state.searchCondition["vullevel"].length > 0
  ) {
    tmpConditions.push({
      field: "vullevel",
      fuzzyable: false,
      value: state.searchCondition["vullevel"].join(",")
    });
  }
  if (
    state.searchCondition["dealStatus"] &&
    state.searchCondition["dealStatus"].length > 0
  ) {
    tmpConditions.push({
      field: "dealStatus",
      fuzzyable: false,
      value: state.searchCondition["dealStatus"].join(",")
    });
  }
  if (state.searchCondition["deptName"]) {
    tmpConditions.push({
      field: "refOrgNameTree",
      fuzzyable: false,
      value: state.searchCondition["deptName"]
    });
  }
  if (state.searchCondition["asset_type_name"]) {
    tmpConditions.push({
      field: "asset_type_name",
      fuzzyable: false,
      value: state.searchCondition["asset_type_name"]
    });
  }
  if ((props.eventInfo && props.eventInfo["ip"]) || assetIp.value.length > 0) {
    tmpConditions.push({
      field: "ip",
      fuzzyable: true,
      operator: "exact",
      value: assetIp.value || props.eventInfo["ip"]
    });
  }
  if (vulName.value && vulName.value.length > 0) {
    tmpConditions.push({
      field: "vulName",
      fuzzyable: true,
      operator: "fuzzy",
      value: vulName.value
    });
  }
};
//查询事件数据
const queryEventData = async () => {
  //设置表格加载状态为true
  state.tableLoading = true;
  state.tableData = [];
  dealFuzzEnable();

  try {
    const tmpConditions = [];

    searchTmpData(tmpConditions);

    //根据条件查询事件列表
    const res = await vulDetailByAssetMethod({
      //查询条件
      conditions: state.columnCondition.value
        ? tmpQuery.value
          ? [state.columnCondition, ...tmpConditions, tmpQuery.value]
          : [state.columnCondition, ...tmpConditions]
        : tmpQuery.value
          ? [...tmpConditions, tmpQuery.value]
          : [...tmpConditions],
      //日期范围
      dateRange: computedDateRange.value,
      // dateRange: 'all',
      //搜索条件
      dealWith: +state.searchCondition["dealWith"],
      dealStatus:
        +state.searchCondition["dealWith"] == 0
          ? ""
          : +state.searchCondition["dealWith"] == 1
            ? "undisposed"
            : +state.searchCondition["dealWith"] == 2
              ? "disposalof"
              : "noNeedHandle",
      // ip: props.eventInfo['ip'],
      // model: "event",
      //当前页码
      pageNum: state.tablePage.currentPage,
      //每页显示条数
      pageSize: state.tablePage.pageSize
    });
    //设置表格数据
    state.tableData = res["data"].rows;

    // 处理动态表头
    tableOption.column.length = 0;
    res["data"]["columns"].forEach(item => {
      // console.log(res['data']['showList'].includes(item['field']));
      if (res["data"]["showList"].includes(item["field"])) {
        if (
          item["field"] == "ip" ||
          item["field"] == "vullevel" ||
          item["field"] == "status" ||
          item["field"] == "vulName" ||
          item["field"] == "vulType" ||
          item["field"] == "dispatch_status"
        ) {
          tableOption.column.unshift({
            hide: item["field"] == "vulsuggest" ? true : false,
            prop: item["field"],
            label: item["name"],
            fieldType: item["fieldType"],
            width:
              item["name"] == "最新发现时间" ||
              item["name"] == "首次发现时间" ||
              item["name"] == "解决方案" ||
              item["name"] == "漏洞名称" ||
              item["name"] == "资产名称" ||
              item["name"] == "漏洞类型" ||
              item["name"] == "资产IP"
                ? item["name"] == "解决方案"
                  ? 300
                  : 200
                : "",
            unit: item["unit"],
            component: item["component"],
            filters: true,
            sortable: true
          });
        } else {
          tableOption.column.push({
            hide: item["field"] == "vulsuggest" ? true : false,
            prop: item["field"],
            label: item["name"],
            fieldType: item["fieldType"],
            width:
              item["name"] == "最新发现时间" ||
              item["name"] == "首次发现时间" ||
              item["name"] == "解决方案" ||
              item["name"] == "漏洞名称" ||
              item["name"] == "资产名称" ||
              item["name"] == "资产类别"
                ? item["name"] == "解决方案"
                  ? 300
                  : 200
                : "",
            unit: item["unit"],
            component: item["component"],
            filters: true,
            sortable: true
          });
        }
      }
    });

    // 处理查询项
    const tmpQuerySearch = [];
    res["data"]["columns"].forEach(item => {
      const obj = { ...item["component"] };
      if (!("disableSearch" in obj)) {
        // if (item['component']['type'] == 'Input') {
        tmpQuerySearch.push({
          value: item["field"],
          label: item["name"]
        });
        // }
      }
    });
    emit("searchQuery", tmpQuerySearch);

    //设置表格总条数
    state.tablePage.total = res["data"].totalElements;
  } catch (e) {
    console.error(e);
  }
  //设置表格加载状态为false
  state.tableLoading = false;
};

//处理模糊标识
function dealFuzzEnable() {
  if (state.columnCondition.fuzzyable) {
    state.columnCondition.operator = "fuzzy";
  } else {
    state.columnCondition.operator = "exact";
  }
}

const segmentChangeHandler = () => resetTablePageAndQuery();

//风险级别改变触发
const riskLevelChangeHandler = (level: string) => {
  resetTablePageAndQuery();
};

const showInfo = ref(false);
const currentInfoRow = ref(null);
//查看事件详情触发
const detailViewHandler = (evt: any) => {
  // emit("event-select", evt);
  // jumpTo("eventDetailInfo");
  showInfo.value = true;
  currentInfoRow.value = evt;
};

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  const selectIds = [];
  selRows.forEach(row => selectIds.push(row.vul_id));
  state.selectedEvents = selectIds;
  state.selectedEventRows = selRows;
};

//导出事件数据触发
const exportEventHandler = () => {
  $confirm(`您确认要导出当前查询条件下的数据么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(async () => {
    $message({
      message: "数据正在导出中...",
      type: "success"
    });
    const tmpConditions = [];
    searchTmpData(tmpConditions);
    await importVulDetailByAssetMethod({
      //查询条件
      conditions: state.columnCondition.value
        ? [state.columnCondition, ...tmpConditions]
        : [...tmpConditions],
      //日期范围
      dateRange: computedDateRange.value,
      dealStatus:
        +state.searchCondition["dealWith"] == 0
          ? ""
          : +state.searchCondition["dealWith"] == 1
            ? "undisposed"
            : +state.searchCondition["dealWith"] == 2
              ? "disposalof"
              : "noNeedHandle",
      //搜索条件
      dealWith: +state.searchCondition["dealWith"],
      // ip: props.eventInfo['ip'],
      // model: "event",
      //当前页码
      pageNum: state.tablePage.currentPage,
      //每页显示条数
      pageSize: state.tablePage.pageSize
    });
  });
};
const showImport = ref(false);
const commandImportType = ref("");

const showHandle = ref(false);
const batchOperationRowsData = ref([]);
const eventDispatchModalVisable = ref(false);
const eventDispatchModalForm = reactive({
  title: "",
  deptId: "",
  userId: [],
  params: []
});
const initEventDispatchModalForm = () => {
  eventDispatchModalForm.title = "";
  eventDispatchModalForm.deptId = "";
  eventDispatchModalForm.userId = [];
  eventDispatchModalForm.params = [];
};
const handleCommand = (command, info = "", onlyOne = "") => {
  initEventDispatchModalForm();
  // console.log(command, state.selectedEventRows);
  if (info == "moreFunction") {
    if (onlyOne == "one") {
      batchOperationRowsData.value = {
        ...command,
        onlyOne
      };
      showHandle.value = true;
      return;
    } else {
      batchOperationRowsData.value = [...state.selectedEventRows];
      showHandle.value = true;
      return;
    }
  } else if (info == "批量派单") {
    let checkWhetherOrderSent = false;
    state.selectedEventRows.forEach(item => {
      if (item["dispatch_status"] == "已派单") {
        checkWhetherOrderSent = true;
        return;
      }
    });
    if (checkWhetherOrderSent) {
      $message({
        message: "当前所选存在已派单，请仅选择未派单的数据",
        type: "info"
      });
      return;
    }

    eventDispatchModalVisable.value = true;
    // const tmpVulArray = state.selectedEventRows.map(item => {
    //     return {
    //         businessId: item['vul_id'],
    //         capitalId: item['ip'],
    //     }
    // })

    // console.log(tmpVulArray.join());
    const tmpVulArray = state.selectedEventRows.map(item => {
      return {
        businessId: item["vul_id"],
        capitalId: item["ip"]
      };
    });
    // console.log(tmpVulArray.join());
    eventDispatchModalForm.params = tmpVulArray;
    // console.log(JSON.stringify(tmpVulArray));
    // bulkOrder({
    //     "params": JSON.stringify(tmpVulArray),
    //     "type": "bug",
    //     "multiTab": false
    // }).then(res => {
    //     $message({
    //         message: res['msg'],
    //         type: "info"
    //     });
    // })
    //     .catch(err => {
    //         $message({
    //             message: err,
    //             type: "info"
    //         });
    //     })
    return;
  } else if (info == "批量验证") {
    const vulIds = state.selectedEventRows.map(item => item["vul_id"]);
    const result = vulIds.join(",");
    // console.log(JSON.stringify(tmpVulArray));
    batchVerification({
      taskType: "0,1,3",
      relatedVulIds: result,
      target: props.eventInfo["ip"],
      name: `test-${Math.random()}`
    }).then(res => {
      $message({
        message: res["msg"],
        type: "info"
      });
    });
    return;
  }
  showImport.value = true;
  commandImportType.value = command;
};

const closeDraw = () => {
  queryEventData();
  showHandle.value = false;
};

// 导入种类
const importTemplate = ref([]);
const templateTypesAxios = async () => {
  const res = await templateTypesMethod();
  importTemplate.value = res["data"];
};

onMounted(() => {
  queryEventData();
  templateTypesAxios();
});

//跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
</script>

<style lang="scss" scoped>
:deep(.el-popper) {
  max-width: 666px !important;
}

:deep(.el-table__row) {
  .el-checkbox {
    margin: 0 auto;
  }
}
</style>
