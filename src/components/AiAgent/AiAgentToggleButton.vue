<template>
  <div class="ai-agent-toggle-button" :class="{ 'button-active': show }">
    <el-button
      :type="show ? 'danger' : 'primary'"
      :loading="loading"
      @click="handleToggle"
      class="toggle-btn"
      :class="{ 'btn-show': show, 'btn-hide': !show }"
    >
      <div class="btn-content">
        <i :class="show ? 'el-icon-close' : 'el-icon-cpu'"></i>
        <span class="btn-text">{{ show ? '隐藏助手' : '智能助手' }}</span>
      </div>
      
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="{ 'active': show }"></div>
      
      <!-- 光泽动画 -->
      <div class="btn-shine"></div>
    </el-button>
  </div>
</template>

<script>
export default {
  name: "AiAgentToggleButton",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    handleToggle() {
      if (!this.loading) {
        this.$emit("toggle");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.ai-agent-toggle-button {
  position: fixed;
  bottom: 80px;
  right: 20px;
  z-index: 1000;
  
  .toggle-btn {
    position: relative;
    min-width: 120px;
    height: 48px;
    border-radius: 24px;
    border: none;
    font-size: 14px;
    font-weight: 600;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }

    &:active {
      transform: translateY(0);
    }

    // 隐藏状态样式（蓝绿渐变）
    &.btn-hide {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      }
    }

    // 显示状态样式（红橙渐变）
    &.btn-show {
      background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
      color: white;

      &:hover {
        background: linear-gradient(135deg, #ff5252 0%, #ff9800 100%);
      }
    }

    .btn-content {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 2;

      i {
        margin-right: 6px;
        font-size: 16px;
      }

      .btn-text {
        font-size: 14px;
      }
    }

    // 状态指示器
    .status-indicator {
      position: absolute;
      top: 6px;
      right: 6px;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;

      &.active {
        background-color: #4caf50;
        box-shadow: 0 0 6px rgba(76, 175, 80, 0.6);
      }
    }

    // 光泽动画
    .btn-shine {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s ease;
    }

    &:hover .btn-shine {
      left: 100%;
    }

    // 加载状态
    &.is-loading {
      pointer-events: none;
      
      .btn-content {
        opacity: 0.7;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    bottom: 60px;
    right: 16px;

    .toggle-btn {
      min-width: 100px;
      height: 44px;
      font-size: 13px;

      .btn-content {
        i {
          font-size: 14px;
        }

        .btn-text {
          font-size: 13px;
        }
      }
    }
  }

  // 动画效果
  &.button-active {
    .toggle-btn {
      animation: pulse 2s infinite;
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  50% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 20px rgba(255, 107, 107, 0.3);
  }
  100% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// 主题适配 - 移除themify，使用CSS变量
.ai-agent-toggle-button {
  .toggle-btn {
    &.btn-hide {
      // 使用CSS变量替代themify
      background: linear-gradient(135deg, var(--color-primary, #409eff) 0%, #764ba2 100%);
    }
  }
}
</style>
