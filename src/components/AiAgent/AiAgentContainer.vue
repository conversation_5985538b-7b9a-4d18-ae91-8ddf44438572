<template>
  <div class="ai-agent-container">
    <!-- 头部控制栏 -->
    <div class="ai-agent-header">
      <div class="ai-agent-title">
        <i class="el-icon-cpu"></i>
        <span>智能助手</span>
      </div>
      <div class="ai-agent-controls">
        <el-button
          type="text"
          icon="el-icon-close"
          @click="handleClose"
          class="close-btn"
        ></el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="ai-agent-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="ai-agent-loading">
        <el-loading
          element-loading-text="智能助手加载中..."
          element-loading-spinner="el-icon-loading"
        ></el-loading>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="ai-agent-error">
        <div class="error-icon">
          <i class="el-icon-warning-outline"></i>
        </div>
        <div class="error-text">
          <h3>智能助手暂时无法使用</h3>
          <p>{{ errorMessage }}</p>
        </div>
        <el-button type="primary" @click="retry">重试</el-button>
      </div>

      <!-- iframe内容 -->
      <iframe
        v-else-if="url"
        :src="url"
        class="ai-agent-iframe"
        frameborder="0"
        @load="handleIframeLoad"
        @error="handleIframeError"
      ></iframe>

      <!-- 占位内容 -->
      <div v-else class="ai-agent-placeholder">
        <div class="placeholder-content">
          <div class="placeholder-icon">
            <i class="el-icon-cpu"></i>
          </div>
          <h3>智能助手</h3>
          <p>为您提供智能化的故障分析和处理建议</p>
          
          <div class="feature-list">
            <div class="feature-item">
              <i class="el-icon-chat-dot-round"></i>
              <span>智能问答</span>
            </div>
            <div class="feature-item">
              <i class="el-icon-data-analysis"></i>
              <span>故障分析</span>
            </div>
            <div class="feature-item">
              <i class="el-icon-lightbulb"></i>
              <span>处理建议</span>
            </div>
          </div>

          <div class="placeholder-note">
            <i class="el-icon-info"></i>
            <span>智能助手正在准备中，敬请期待...</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AiAgentContainer",
  props: {
    url: {
      type: String,
      default: "",
    },
    config: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      error: false,
      errorMessage: "",
    };
  },
  watch: {
    url: {
      handler(newUrl) {
        if (newUrl) {
          this.loading = true;
          this.error = false;
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleIframeLoad() {
      this.loading = false;
      this.error = false;
    },
    handleIframeError() {
      this.loading = false;
      this.error = true;
      this.errorMessage = "智能助手页面加载失败，请检查网络连接";
    },
    retry() {
      if (this.url) {
        this.loading = true;
        this.error = false;
        // 重新加载iframe
        this.$nextTick(() => {
          const iframe = this.$el.querySelector(".ai-agent-iframe");
          if (iframe) {
            iframe.src = this.url;
          }
        });
      }
    },
    // 与iframe通信的方法
    postMessage(message) {
      const iframe = this.$el.querySelector(".ai-agent-iframe");
      if (iframe && iframe.contentWindow) {
        iframe.contentWindow.postMessage(message, "*");
      }
    },
    // 处理来自智能助手iframe的消息
    handleMessage(event) {
      // 处理来自智能助手iframe的消息
      if (event.origin !== window.location.origin) {
        // 可以在这里添加安全检查
        console.log("Received message from AI agent:", event.data);
        this.$emit("message", event.data);
      }
    },
  },
  mounted() {
    // 监听来自iframe的消息
    window.addEventListener("message", this.handleMessage);
  },
  beforeDestroy() {
    window.removeEventListener("message", this.handleMessage);
  },
};
</script>

<style lang="scss" scoped>
.ai-agent-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.ai-agent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #fff;

  .ai-agent-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
    color: #303133;

    i {
      margin-right: 8px;
      font-size: 18px;
      color: #409eff;
    }
  }

  .close-btn {
    padding: 4px;
    color: #909399;

    &:hover {
      color: #303133;
    }
  }
}

.ai-agent-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.ai-agent-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.ai-agent-loading {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-agent-error {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;

  .error-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #e6a23c;
  }

  .error-text {
    margin-bottom: 24px;

    h3 {
      margin: 0 0 8px 0;
      color: #303133;
    }

    p {
      margin: 0;
      color: #909399;
    }
  }
}

.ai-agent-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;

  .placeholder-content {
    text-align: center;
    max-width: 300px;

    .placeholder-icon {
      font-size: 64px;
      margin-bottom: 24px;
      color: #409eff;
    }

    h3 {
      margin: 0 0 12px 0;
      font-size: 20px;
      color: #303133;
    }

    p {
      margin: 0 0 32px 0;
      color: #909399;
    }

    .feature-list {
      margin-bottom: 32px;

      .feature-item {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12px;
        color: #909399;

        i {
          margin-right: 8px;
          color: #409eff;
        }
      }
    }

    .placeholder-note {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 16px;
      background-color: #f5f7fa;
      color: #909399;
      border-radius: 4px;

      i {
        margin-right: 8px;
        color: #909399;
      }
    }
  }
}
</style>
