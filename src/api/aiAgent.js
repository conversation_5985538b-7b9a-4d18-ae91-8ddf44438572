/**
 * 智能体相关API接口
 */

import request from '@/utils/request';

/**
 * 检查智能体功能是否可用
 * @param {Object} params - 检查参数
 * @param {string} params.woId - 工单ID
 * @param {string} params.professionId - 专业类型ID
 * @param {string} params.routeName - 路由名称
 * @returns {Promise} 返回检查结果
 */
export function checkAiAgentAvailability(params) {
  return request({
    url: '/api/ai-agent/check-availability',
    method: 'post',
    data: params
  });
}

/**
 * 获取智能体配置信息
 * @param {Object} params - 配置参数
 * @param {string} params.woId - 工单ID
 * @param {string} params.professionId - 专业类型ID
 * @returns {Promise} 返回配置信息
 */
export function getAiAgentConfig(params) {
  return request({
    url: '/api/ai-agent/config',
    method: 'get',
    params: params
  });
}

/**
 * 获取智能体页面URL
 * @param {Object} params - 参数
 * @param {string} params.woId - 工单ID
 * @param {string} params.userId - 用户ID
 * @param {string} params.token - 用户token
 * @returns {Promise} 返回智能体URL
 */
export function getAiAgentUrl(params) {
  return request({
    url: '/api/ai-agent/url',
    method: 'post',
    data: params
  });
}

/**
 * 记录智能体使用日志
 * @param {Object} params - 日志参数
 * @param {string} params.action - 操作类型 (show/hide/interact)
 * @param {string} params.woId - 工单ID
 * @param {Object} params.metadata - 元数据
 * @returns {Promise} 返回记录结果
 */
export function logAiAgentUsage(params) {
  return request({
    url: '/api/ai-agent/log',
    method: 'post',
    data: params
  });
}

/**
 * 获取用户智能体偏好设置
 * @param {string} userId - 用户ID
 * @returns {Promise} 返回用户偏好设置
 */
export function getUserAiAgentPreferences(userId) {
  return request({
    url: `/api/ai-agent/preferences/${userId}`,
    method: 'get'
  });
}

/**
 * 保存用户智能体偏好设置
 * @param {Object} params - 偏好设置参数
 * @param {string} params.userId - 用户ID
 * @param {Object} params.preferences - 偏好设置
 * @returns {Promise} 返回保存结果
 */
export function saveUserAiAgentPreferences(params) {
  return request({
    url: '/api/ai-agent/preferences',
    method: 'post',
    data: params
  });
}

/**
 * 获取智能体支持的专业类型列表
 * @returns {Promise} 返回支持的专业类型列表
 */
export function getSupportedProfessions() {
  return request({
    url: '/api/ai-agent/supported-professions',
    method: 'get'
  });
}

/**
 * 智能体健康检查
 * @returns {Promise} 返回健康状态
 */
export function healthCheck() {
  return request({
    url: '/api/ai-agent/health',
    method: 'get'
  });
}

// 模拟API响应（用于开发阶段）
export const mockApiResponses = {
  checkAvailability: {
    success: true,
    data: {
      available: true,
      reason: 'AI agent is available for this work order',
      supportedFeatures: ['chat', 'analysis', 'suggestions']
    }
  },
  
  getConfig: {
    success: true,
    data: {
      title: '智能助手',
      version: '1.0.0',
      features: ['智能问答', '故障分析', '处理建议'],
      theme: 'default',
      settings: {
        autoShow: false,
        defaultWidth: 33.33,
        enableNotifications: true
      }
    }
  },
  
  getUrl: {
    success: true,
    data: {
      url: '', // 暂时为空，显示占位内容
      expires: Date.now() + 3600000, // 1小时后过期
      token: 'mock-token-123'
    }
  },
  
  getSupportedProfessions: {
    success: true,
    data: {
      professions: [
        { id: 31, name: '核心网', enabled: true },
        { id: 32, name: '无线网', enabled: false },
        { id: 33, name: '传输网', enabled: false }
      ]
    }
  }
};

/**
 * 开发环境下的模拟API调用
 * 在实际部署时应该移除或通过环境变量控制
 */
export function mockApiCall(apiName, params = {}) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const response = mockApiResponses[apiName];
      if (response) {
        resolve(response);
      } else {
        resolve({
          success: false,
          message: `Mock API ${apiName} not found`
        });
      }
    }, Math.random() * 1000 + 500); // 模拟网络延迟
  });
}
