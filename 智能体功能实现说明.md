# 智能体页面嵌套功能实现说明

## 功能概述

本项目已成功实现了智能体页面嵌套功能，采用框架级别的实现方案，具有以下特点：

- **最小侵入性**：在现有框架基础上添加功能，不破坏原有代码结构
- **全局可用**：在框架层面实现，所有支持的页面都可以使用
- **响应式设计**：支持不同屏幕尺寸的自适应布局
- **优雅降级**：在不支持的页面或环境下自动隐藏
- **扩展性强**：预留了丰富的接口和配置选项

## 实现架构

### 1. 核心组件结构

```
src/
├── components/AiAgent/              # 智能体组件
│   ├── AiAgentContainer.vue        # 智能体容器组件
│   └── AiAgentToggleButton.vue     # 切换按钮组件
├── store/modules/aiAgent.js         # Vuex状态管理
├── api/aiAgent.js                   # API接口定义
├── styles/aiAgent.scss              # 智能体样式
└── framework/views/layouts/         # 框架布局修改
    ├── FrameLayout.vue             # 主布局组件（已修改）
    └── index.vue                   # 布局入口（已修改）
```

### 2. 技术实现要点

#### 布局实现
- 使用固定定位(fixed positioning)实现智能体区域
- 主内容区域动态调整宽度：智能体显示时占2/3，隐藏时占100%
- 智能体区域从页面顶部开始，覆盖整个页面高度
- 支持响应式适配，移动端全屏显示

#### 状态管理
- 使用Vuex管理智能体的全局状态
- 支持用户偏好设置的持久化存储
- 提供完整的状态变更和错误处理机制

#### 组件通信
- 预留iframe通信机制，支持与智能体页面的双向通信
- 提供事件监听和消息传递接口

## 功能特性

### 1. 显示控制
- **悬浮按钮**：位于页面右下角，支持一键切换
- **状态指示**：按钮颜色和文字根据当前状态动态变化
- **动画效果**：平滑的显示/隐藏过渡动画

### 2. 布局适配
- **桌面端**：智能体区域占1/3宽度，主内容占2/3宽度
- **平板端**：智能体区域占40%宽度，主内容占60%宽度
- **移动端**：智能体区域全屏显示，带遮罩层效果

### 3. 内容展示
- **iframe嵌入**：支持嵌入外部智能体页面
- **占位内容**：当没有URL时显示功能预览
- **加载状态**：显示加载动画和进度提示
- **错误处理**：提供错误提示和重试机制

### 4. 用户体验
- **记忆功能**：记住用户的显示偏好设置
- **快捷操作**：支持键盘快捷键和手势操作
- **无障碍支持**：符合WCAG无障碍标准

## 配置选项

### 1. 支持的页面类型
当前支持以下页面类型的智能体功能：
- 工单详情页面 (`common_orderDetail`)
- 通用工单详情 (`CommonWoDetail`)
- 包含 `orderDetail` 或 `WoDetail` 的路径

### 2. 专业类型支持
- **开发环境**：支持所有专业类型（便于测试）
- **生产环境**：可配置支持的专业类型列表
- **默认配置**：核心网专业（ID: 31）

### 3. 用户偏好设置
```javascript
userPreferences: {
  autoShow: false,        // 是否自动显示
  defaultWidth: 33.33,    // 默认宽度百分比
  rememberState: true,    // 是否记住显示状态
}
```

## API接口

### 1. 可用性检查
```javascript
// 检查智能体功能是否可用
checkAiAgentAvailability({
  woId: '工单ID',
  professionId: '专业类型ID',
  routeName: '路由名称'
})
```

### 2. 配置获取
```javascript
// 获取智能体配置信息
getAiAgentConfig({
  woId: '工单ID',
  professionId: '专业类型ID'
})
```

### 3. URL获取
```javascript
// 获取智能体页面URL
getAiAgentUrl({
  woId: '工单ID',
  userId: '用户ID',
  token: '用户token'
})
```

## 使用方法

### 1. 基本使用
智能体功能会在支持的页面自动启用，用户可以通过以下方式操作：

1. **显示智能体**：点击右下角的"智能助手"按钮
2. **隐藏智能体**：点击"隐藏助手"按钮或智能体区域的关闭按钮
3. **调整大小**：在桌面端可以拖拽边界调整宽度（预留功能）

### 2. 开发集成
如需在新页面中启用智能体功能：

1. **添加页面支持**：在 `checkPageSupport` 函数中添加页面标识
2. **配置专业类型**：在 `supportedProfessions` 中添加支持的专业类型
3. **自定义配置**：通过API返回特定的配置信息

### 3. 样式定制
可以通过修改 `src/styles/aiAgent.scss` 文件来定制样式：

```scss
// 修改智能体区域宽度
.ai-agent-panel {
  width: 25% !important; // 自定义宽度
}

// 修改按钮样式
.ai-agent-toggle-button .toggle-btn {
  // 自定义按钮样式
}
```

## 扩展功能

### 1. 通信机制
智能体容器支持与iframe内容的双向通信：

```javascript
// 发送消息到智能体
this.$refs.aiAgentContainer.postMessage({
  type: 'workOrderData',
  data: workOrderInfo
});

// 监听来自智能体的消息
this.$on('ai-agent-message', (message) => {
  console.log('Received from AI agent:', message);
});
```

### 2. 事件钩子
提供丰富的事件钩子用于扩展：

```javascript
// 智能体显示前
beforeAiAgentShow(config) {
  // 自定义逻辑
}

// 智能体隐藏后
afterAiAgentHide() {
  // 清理逻辑
}
```

### 3. 插件机制
支持通过插件扩展智能体功能：

```javascript
// 注册智能体插件
Vue.use(AiAgentPlugin, {
  // 插件配置
});
```

## 注意事项

### 1. 性能考虑
- 智能体区域采用懒加载机制，只在需要时才加载内容
- iframe内容会在隐藏时保持状态，避免重复加载
- 大量数据交互时建议使用防抖和节流机制

### 2. 安全考虑
- iframe通信需要验证消息来源
- 敏感数据传递需要加密处理
- 定期检查智能体页面的安全性

### 3. 兼容性
- 支持现代浏览器（Chrome 60+, Firefox 55+, Safari 12+）
- IE浏览器需要polyfill支持
- 移动端浏览器需要特殊适配

## 后续优化建议

1. **性能优化**：实现智能体内容的预加载和缓存机制
2. **用户体验**：添加拖拽调整大小功能
3. **功能扩展**：支持多个智能体同时显示
4. **数据分析**：添加使用统计和分析功能
5. **国际化**：支持多语言界面

## 技术支持

如有问题或需要技术支持，请联系开发团队或查看相关文档。
