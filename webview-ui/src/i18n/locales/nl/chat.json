{"greeting": "Welkom bij Roo <PERSON>", "task": {"title": "<PERSON><PERSON>", "seeMore": "<PERSON><PERSON> weer<PERSON><PERSON>", "seeLess": "<PERSON><PERSON> weer<PERSON><PERSON>", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "API-kosten:", "contextWindow": "Contextlengte:", "closeAndStart": "<PERSON><PERSON> sluiten en een nieuwe starten", "export": "Taakgeschiedenis exporteren", "delete": "<PERSON><PERSON> verwijderen (<PERSON><PERSON> + <PERSON><PERSON> om bevestiging over te slaan)", "condenseContext": "Context intelligent <PERSON><PERSON><PERSON><PERSON>"}, "unpin": "Losmaken", "pin": "Vastmaken", "retry": {"title": "Opnieuw proberen", "tooltip": "<PERSON><PERSON><PERSON> de bewerking opnieuw"}, "startNewTask": {"title": "<PERSON><PERSON><PERSON> taak starten", "tooltip": "Begin een nieuwe taak"}, "proceedAnyways": {"title": "<PERSON><PERSON> doorgaan", "tooltip": "Ga door terwijl het commando wordt uitgevoerd"}, "save": {"title": "Opsla<PERSON>", "tooltip": "Bestandswijzigingen opslaan"}, "tokenProgress": {"availableSpace": "Beschikbare ruimte: {{amount}} tokens", "tokensUsed": "Gebruikte tokens: {{used}} van {{total}}", "reservedForResponse": "Gereserveerd voor modelantwoord: {{amount}} tokens"}, "reject": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Deze actie weigeren"}, "completeSubtaskAndReturn": "Subtaak voltooien en terugkeren", "approve": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Deze actie goedkeuren"}, "runCommand": {"title": "Commando uitvoeren", "tooltip": "<PERSON><PERSON>r dit commando uit"}, "proceedWhileRunning": {"title": "Doorgaan tijdens uitvoeren", "tooltip": "Ga door ondanks waarschuwingen"}, "killCommand": {"title": "Commando stoppen", "tooltip": "Huidig commando stoppen"}, "resumeTask": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON>a door met de huidige taak"}, "terminate": {"title": "Beëindigen", "tooltip": "Beëindig de huidige taak"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> de huidige bewerking"}, "scrollToBottom": "<PERSON><PERSON> naar on<PERSON>aan de chat", "about": "<PERSON><PERSON>, refactor en debug code met AI-assistentie. Bekijk onze <DocsLink>documentatie</DocsLink> voor meer informatie.", "onboarding": "<PERSON> in deze werkruimte is leeg.", "rooTips": {"boomerangTasks": {"title": "Boomerang-taken", "description": "Splits taken op in kleinere, beheers<PERSON>e delen"}, "stickyModels": {"title": "Vastgezette modellen", "description": "Elke modus onthoudt je laatst gebruikte model"}, "tools": {"title": "Tools", "description": "Laat de AI problemen oplossen door te browsen, commando's uit te voeren en meer"}, "customizableModes": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> modi", "description": "Gespecialiseerde persona's met hun eigen gedrag en toegewezen modellen"}}, "selectMode": "Selecteer modus voor interactie", "selectApiConfig": "Selecteer API-configuratie", "enhancePrompt": "Prompt verbeteren met extra context", "enhancePromptDescription": "De knop 'Prompt verbeteren' helpt je prompt te verbeteren door extra context, verduid<PERSON><PERSON>ing of herformulering te bieden. <PERSON>beer hier een prompt te typen en klik opnieuw op de knop om te zien hoe het werkt.", "addImages": "Afbeeldingen toevoegen aan bericht", "sendMessage": "Bericht verzenden", "typeMessage": "Typ een bericht...", "typeTask": "<PERSON>p hier je taak...", "addContext": "@ om context toe te voegen, / om van modus te wisselen", "dragFiles": "houd shift ingedrukt om bestanden te slepen", "dragFilesImages": "houd shift ingedrukt om bestanden/afbeeldingen te slepen", "errorReadingFile": "Fout bij het lezen van bestand:", "noValidImages": "Er zijn geen geldige afbeeldingen verwerkt", "separator": "Scheidingsteken", "edit": "Bewerken...", "forNextMode": "voor volgende modus", "apiRequest": {"title": "API-verzoek", "failed": "API-verz<PERSON>k mis<PERSON>t", "streaming": "API-verzoek...", "cancelled": "API-verz<PERSON>k g<PERSON>", "streamingFailed": "API-streaming mislukt"}, "checkpoint": {"initial": "Initiële checkpoint", "regular": "Checkpoint", "initializingWarning": "Checkpoint wordt nog steeds geïnitialiseerd... Als dit te lang duurt, kun je checkpoints uitschakelen in de <settingsLink>instellingen</settingsLink> en je taak opnieuw starten.", "menu": {"viewDiff": "Bekijk verschil", "restore": "Herstel checkpoint", "restoreFiles": "<PERSON><PERSON><PERSON>", "restoreFilesDescription": "Herstelt de bestanden van je project naar een momentopname die op dit punt is gemaakt.", "restoreFilesAndTask": "Bestanden & taak herstellen", "confirm": "Bevestigen", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Deze actie kan niet ongedaan worden gemaakt.", "restoreFilesAndTaskDescription": "Herstelt de bestanden van je project naar een momentopname die op dit punt is gemaakt en verwijdert alle berichten na dit punt."}, "current": "<PERSON><PERSON><PERSON>"}, "instructions": {"wantsToFetch": "<PERSON>oo wil g<PERSON><PERSON><PERSON><PERSON> instructies op<PERSON>n om te helpen met de huidige taak"}, "fileOperations": {"wantsToRead": "<PERSON>oo wil dit bestand lezen:", "wantsToReadOutsideWorkspace": "<PERSON>oo wil dit bestand buiten de werkruimte lezen:", "didRead": "<PERSON>oo heeft dit bestand gelezen:", "wantsToEdit": "<PERSON>oo wil dit bestand bewerken:", "wantsToEditOutsideWorkspace": "<PERSON>oo wil dit bestand buiten de werkruimte bewerken:", "wantsToCreate": "<PERSON>oo wil een nieuw bestand aanmaken:", "wantsToSearchReplace": "<PERSON><PERSON> wil zoeken en vervangen in dit bestand:", "didSearchReplace": "<PERSON>oo heeft zoeken en vervangen uitgevoerd op dit bestand:", "wantsToInsert": "<PERSON>oo wil inhoud invoegen in dit bestand:", "wantsToInsertWithLineNumber": "<PERSON>oo wil inhoud invoegen in dit bestand op regel {{lineNumber}}:", "wantsToInsertAtEnd": "<PERSON>oo wil inhoud toevoegen aan het einde van dit bestand:"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON>oo wil de bovenliggende bestanden in deze map bekijken:", "didViewTopLevel": "<PERSON><PERSON> heeft de bovenliggende bestanden in deze map bekeken:", "wantsToViewRecursive": "<PERSON><PERSON> wil alle bestanden in deze map recursief bekijken:", "didViewRecursive": "<PERSON>oo heeft alle bestanden in deze map recursief bekeken:", "wantsToViewDefinitions": "Roo wil broncode-definitienamen bekijken die in deze map worden gebruikt:", "didViewDefinitions": "Roo heeft broncode-definitienamen bekeken die in deze map worden gebruikt:", "wantsToSearch": "Roo wil deze map doorzoeken op <code>{{regex}}</code>:", "didSearch": "Roo heeft deze map doorzocht op <code>{{regex}}</code>:"}, "commandOutput": "Commando-uitvoer", "response": "Antwoord", "arguments": "Argumenten", "mcp": {"wantsToUseTool": "Roo wil een tool gebruiken op de {{serverName}} MCP-server:", "wantsToAccessResource": "<PERSON>oo wil een bron benaderen op de {{serverName}} MCP-server:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON> wil overschakelen naar {{mode}} modus", "wantsToSwitchWithReason": "<PERSON><PERSON> wil overschakelen naar {{mode}} modus omdat: {{reason}}", "didSwitch": "<PERSON><PERSON> is overgeschakeld naar {{mode}} modus", "didSwitchWithReason": "<PERSON><PERSON> is overgeschakeld naar {{mode}} modus omdat: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON>oo wil een nieuwe subtaak aanmaken in {{mode}} modus:", "wantsToFinish": "<PERSON>oo wil deze subtaak voltooien", "newTaskContent": "Subtaak-instructies", "completionContent": "Subtaak voltooid", "resultContent": "Subtaakresultaten", "defaultResult": "<PERSON><PERSON> <PERSON><PERSON><PERSON> met de volgende taak.", "completionInstructions": "Subtaak voltooid! Je kunt de resultaten bekijken en eventuele correcties of volgende stappen voorstellen. Als alles goed is, bevestig dan om het resultaat terug te sturen naar de hoofdtaak."}, "questions": {"hasQuestion": "<PERSON>oo heeft een vraag:"}, "taskCompleted": "Taak voltooid", "error": "Fout", "diffError": {"title": "Bewerking mislukt"}, "troubleMessage": "<PERSON>oo on<PERSON>dt problemen...", "powershell": {"issues": "Het lijkt erop dat je problemen hebt met Windows PowerShell, zie deze"}, "autoApprove": {"title": "Automatisch goedkeuren:", "none": "<PERSON><PERSON>", "description": "Met automatisch goedkeuren kan Roo Code acties uitvoeren zonder om toestemming te vragen. <PERSON><PERSON><PERSON> dit alleen in voor acties die je volledig vertrouwt. Meer gedetailleerde configuratie besch<PERSON> in de <settingsLink>Instellingen</settingsLink>."}, "announcement": {"title": "🎉 Roo Code {{version}} uitgebracht", "description": "Roo Code {{version}} brengt krachtige nieuwe functies en verbeteringen op basis van jouw feedback.", "feature1": "<bold>Gemini 2.5 Flash preview-modellen</bold>: <PERSON><PERSON><PERSON> tot de nieuwste Gemini Flash-modellen voor snellere en efficiëntere antwoorden", "feature2": "<bold>Intelligente contextcompressie</bold>: <PERSON><PERSON><PERSON> knop in de taakheader waarmee je content intelligent kunt comprimeren met visuele feedback", "feature3": "<bold>YAML-ondersteuning voor modusdefinities</bold>: <PERSON><PERSON> en pas modi eenvoudiger aan met YAML-ondersteuning", "hideButton": "Aankondiging verbergen", "detailsDiscussLinks": "Meer details en discussie in <discordLink>Discord</discordLink> en <redditLink>Reddit</redditLink> 🚀", "whatsNew": "Wat is er nieuw"}, "reasoning": {"thinking": "Denkt na", "seconds": "{{count}}s"}, "contextCondense": {"title": "Context same<PERSON>", "condensing": "Context aan het samenvatten...", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "<PERSON><PERSON><PERSON><PERSON> naar invoer (zelfde als shift + klik)"}, "browser": {"rooWantsToUse": "<PERSON>oo wil de browser gebruiken:", "consoleLogs": "Console-logboeken", "noNewLogs": "(Geen nieuwe logboeken)", "screenshot": "Browserschermopname", "cursor": "cursor", "navigation": {"step": "Stap {{current}} van {{total}}", "previous": "Vorige", "next": "Volgende"}, "sessionStarted": "<PERSON>rows<PERSON>ssie gestart", "actions": {"title": "Browse-actie: ", "launch": "Browser starten op {{url}}", "click": "<PERSON>lik ({{coordinate}})", "type": "Typ \"{{text}}\"", "scrollDown": "<PERSON><PERSON> naar beneden", "scrollUp": "<PERSON><PERSON> naar boven", "close": "Browser sluiten"}}, "codeblock": {"tooltips": {"expand": "Codeblok uitvouwen", "collapse": "Codeblok <PERSON>wen", "enable_wrap": "Regelafbreking inschakelen", "disable_wrap": "Regelafbreking uitschakelen", "copy_code": "Code kopiëren"}}, "systemPromptWarning": "WAARSCHUWING: Aangepaste systeemprompt actief. Dit kan de functionaliteit ernstig verstoren en onvoorspelbaar gedrag veroorzaken.", "shellIntegration": {"title": "Waarschuwing commando-uitvoering", "description": "Je commando wordt uitgevoerd zonder VSCode-terminal shell-integratie. Om deze waarschuwing te onderdrukken kun je shell-integratie uitschakelen in het gedeelte <strong>Terminal</strong> van de <settingsLink>Roo Code-instellingen</settingsLink> of de VSCode-terminalintegratie oplossen via de onderstaande link.", "troubleshooting": "Klik hier voor shell-integratie documentatie."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Limiet voor automatisch goedgekeurde verzoeken bereikt", "description": "<PERSON>oo heeft de automatisch goedgekeurde limiet van {{count}} API-verzoek(en) bereikt. Wil je de teller resetten en doorgaan met de taak?", "button": "Resetten en doorgaan"}}, "codebaseSearch": {"wantsToSearch": "Roo wil de codebase doorzoeken op <code>{{query}}</code>:", "wantsToSearchWithPath": "Roo wil de codebase doorzoeken op <code>{{query}}</code> in <code>{{path}}</code>:", "didSearch": "{{count}} resultaat/resultaten gevonden voor <code>{{query}}</code>:"}}