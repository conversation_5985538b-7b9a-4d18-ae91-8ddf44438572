# Roo Code 学习管理网站

> 基于 Roo Code 项目的系统性学习管理工具，帮助您高效掌握现代前端开发技能

## 🌟 功能特色

### 📊 学习进度管理
- **5个学习阶段**：项目结构理解、架构模式、TypeScript类型安全、前端组件状态管理、算法性能优化
- **详细任务清单**：每个阶段包含具体的学习任务和练习项目
- **进度可视化**：直观的进度条和完成度统计
- **技能雷达图**：多维度技能掌握程度展示

### 📈 数据统计分析
- **学习时间记录**：每日学习时长统计和趋势分析
- **学习效率分析**：任务完成率和学习效率图表
- **连续学习天数**：激励持续学习的成就系统
- **周报月报**：定期学习总结和反思

### 📚 集成文档查看
- **内置文档浏览器**：直接查看学习路线图、快速开始指南等
- **Markdown 渲染**：完整支持 Markdown 格式文档
- **代码语法高亮**：使用 Prism.js 提供专业的代码展示
- **目录导航**：自动生成文档目录，快速定位内容

### 🎯 点击式学习任务启动（新功能）
- **任务文件关联**：每个学习任务关联相关的源代码文件
- **一键启动学习**：点击任务即可查看所有相关文件
- **智能文件展示**：自动加载和展示任务相关的学习材料
- **学习重点标注**：为每个文件提供学习重点和关键知识点
- **学习指南集成**：每个任务包含详细的学习指南和实践建议

### 💾 数据持久化
- **本地存储**：使用 localStorage 确保数据不丢失
- **数据导入导出**：支持 JSON 格式的数据备份和恢复
- **多设备同步**：通过导入导出实现跨设备数据同步

### 🎨 用户体验
- **响应式设计**：完美支持桌面和移动端访问
- **深色/浅色主题**：根据个人喜好切换主题
- **专注模式**：隐藏干扰元素，专注学习内容
- **现代化界面**：参考流行学习管理系统的设计理念

## 🚀 快速开始

### 环境要求
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 支持 ES6+ 和 localStorage
- 建议使用 VS Code Live Server 插件进行本地开发

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd Roo-Code/learning-website
   ```

2. **使用 VS Code Live Server**
   - 安装 Live Server 扩展
   - 右键点击 `index.html`
   - 选择 "Open with Live Server"
   - 浏览器自动打开 `http://localhost:5500`

3. **或使用其他本地服务器**
   ```bash
   # 使用 Python
   python -m http.server 8000

   # 使用 Node.js
   npx serve .

   # 使用 PHP
   php -S localhost:8000
   ```

### 首次使用

1. **设置学习目标**
   - 进入"设置"页面
   - 设置每日学习目标时间
   - 配置提醒和通知偏好

2. **开始学习计划**
   - 查看"学习计划"页面
   - 了解5个学习阶段的内容
   - 开始第一个阶段的学习任务

3. **记录学习时间**
   - 在"进度跟踪"页面记录每日学习时间
   - 添加学习内容描述
   - 查看学习统计和趋势

## 📱 页面功能说明

### 🏠 仪表板
- **总体进度概览**：显示整体学习完成度
- **今日任务**：当天需要完成的学习任务
- **学习统计**：今日、本周、总计学习时间
- **技能雷达图**：6个维度的技能掌握程度
- **学习趋势图**：最近7天的学习时间趋势
- **阶段进度**：5个学习阶段的完成情况

### 📅 学习计划
- **阶段式学习**：5个渐进式学习阶段
- **任务管理**：每个阶段的具体学习任务
- **进度跟踪**：实时更新任务完成状态
- **时间估算**：每个任务的预计学习时间

### 📊 进度跟踪
- **时间记录**：添加和管理学习时间记录
- **数据可视化**：学习时间趋势和进度图表
- **学习历史**：查看历史学习记录
- **数据导出**：导出学习数据进行分析

### 📖 文档查看
- **学习路线图**：完整的学习计划和指导
- **快速开始指南**：环境搭建和入门指导
- **进度跟踪表**：学习进度记录模板
- **实践项目**：5个渐进式实践项目详解

### 🚀 实践项目
- **项目概览**：5个实践项目的难度和时间安排
- **进度管理**：项目开始、进行中、完成状态
- **项目详情**：每个项目的具体要求和验收标准

### ⚙️ 设置
- **数据管理**：导入、导出、清除学习数据
- **个人偏好**：学习目标、提醒设置
- **主题切换**：浅色/深色主题选择
- **关于信息**：系统版本和使用说明

## 🎯 学习路径建议

### 第一周：环境搭建和基础理解
1. 完成 Roo Code 项目环境搭建
2. 理解 Monorepo 架构和 Turbo 构建系统
3. 每天记录学习时间和内容
4. 完成第一阶段的基础任务

### 第二周：架构模式深入学习
1. 学习工厂模式和策略模式
2. 分析 Roo Code 中的设计模式应用
3. 开始 Mini Monorepo 实践项目
4. 保持每日学习记录

### 第三-四周：TypeScript 和前端开发
1. 深入学习 Zod + TypeScript 类型安全
2. 掌握 React 状态管理模式
3. 完成 Type-Safe Config 和 React State Manager 项目
4. 分析学习效率，调整学习计划

### 第五-六周：扩展开发和性能优化
1. 学习 VS Code 扩展开发
2. 掌握算法优化和性能监控
3. 完成 VS Code Extension 和 Performance Optimizer 项目
4. 准备技能展示和作品集

## 📊 数据结构说明

### 学习任务数据
```javascript
{
  id: "task_001",
  title: "理解 package.json 配置",
  stage: "stage1",
  description: "深入分析项目的 package.json 文件",
  duration: 1.5,
  completed: false,
  completedAt: null,
  createdAt: "2024-12-01T10:00:00Z"
}
```

### 时间记录数据
```javascript
{
  id: "record_001",
  date: "2024-12-01",
  hours: 2.5,
  content: "学习 Monorepo 架构，完成环境搭建",
  createdAt: "2024-12-01T20:00:00Z"
}
```

### 技能等级数据
```javascript
{
  monorepo: 75,        // Monorepo 架构掌握度
  typescript: 60,      // TypeScript 技能水平
  react: 80,          // React 开发能力
  algorithms: 45,     // 算法优化技能
  vscode: 30,         // VS Code 扩展开发
  architecture: 55    // 系统架构设计
}
```

## 🔧 技术实现

### 前端技术栈
- **HTML5 + CSS3**：现代化的用户界面
- **原生 JavaScript (ES6+)**：无框架依赖，轻量高效
- **Chart.js**：数据可视化图表
- **Marked.js**：Markdown 文档渲染
- **Prism.js**：代码语法高亮
- **Font Awesome**：图标库

### 数据存储
- **localStorage**：浏览器本地存储
- **JSON 格式**：数据序列化和反序列化
- **数据版本控制**：支持数据迁移和升级

### 响应式设计
- **CSS Grid + Flexbox**：灵活的布局系统
- **媒体查询**：适配不同屏幕尺寸
- **移动端优化**：触摸友好的交互设计

## 🤝 贡献指南

### 报告问题
如果您发现任何问题或有改进建议，请：
1. 检查是否已有相关 issue
2. 创建新的 issue 并详细描述问题
3. 提供复现步骤和环境信息

### 功能建议
欢迎提出新功能建议：
1. 描述功能的使用场景
2. 说明预期的用户体验
3. 考虑技术实现的可行性

### 代码贡献
1. Fork 项目到您的 GitHub 账户
2. 创建功能分支进行开发
3. 确保代码质量和注释完整
4. 提交 Pull Request 并描述更改内容

## 📄 许可证

本项目基于 MIT 许可证开源，详见 [LICENSE](../LICENSE) 文件。

## 🙏 致谢

- **Roo Code 项目**：提供了优秀的学习素材和架构参考
- **开源社区**：Chart.js、Marked.js、Prism.js 等优秀的开源库
- **设计灵感**：现代化学习管理系统的界面设计理念

---

**开始您的 Roo Code 学习之旅吧！** 🚀

如有任何问题，请查看文档或联系项目维护者。
