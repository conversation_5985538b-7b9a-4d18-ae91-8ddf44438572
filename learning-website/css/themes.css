/* 主题变量定义 */

/* 浅色主题 */
.theme-light {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --primary-light: #dbeafe;
    
    --success-color: #10b981;
    --success-dark: #059669;
    --success-light: #d1fae5;
    
    --warning-color: #f59e0b;
    --warning-dark: #d97706;
    --warning-light: #fef3c7;
    
    --danger-color: #ef4444;
    --danger-dark: #dc2626;
    --danger-light: #fee2e2;
    
    --gray-light: #f3f4f6;
    --gray-dark: #6b7280;
    
    --bg-color: #f8fafc;
    --card-bg: #ffffff;
    --sidebar-bg: #ffffff;
    --hover-bg: #f1f5f9;
    
    --text-color: #1e293b;
    --text-secondary: #64748b;
    
    --border-color: #e2e8f0;
    
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 深色主题 */
.theme-dark {
    --primary-color: #60a5fa;
    --primary-dark: #3b82f6;
    --primary-light: #1e3a8a;
    
    --success-color: #34d399;
    --success-dark: #10b981;
    --success-light: #064e3b;
    
    --warning-color: #fbbf24;
    --warning-dark: #f59e0b;
    --warning-light: #78350f;
    
    --danger-color: #f87171;
    --danger-dark: #ef4444;
    --danger-light: #7f1d1d;
    
    --gray-light: #374151;
    --gray-dark: #9ca3af;
    
    --bg-color: #0f172a;
    --card-bg: #1e293b;
    --sidebar-bg: #1e293b;
    --hover-bg: #334155;
    
    --text-color: #f1f5f9;
    --text-secondary: #94a3b8;
    
    --border-color: #334155;
    
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* 主题切换动画 */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 深色主题特殊样式调整 */
.theme-dark .progress-circle {
    background: conic-gradient(var(--primary-color) 0deg, var(--border-color) 0deg);
}

.theme-dark .progress-circle::before {
    background: var(--card-bg);
}

.theme-dark .document-content pre {
    background: var(--bg-color);
}

.theme-dark .document-content code {
    background: var(--bg-color);
}

.theme-dark .records-table th {
    background: var(--bg-color);
}

.theme-dark .stat-item {
    background: var(--bg-color);
}

.theme-dark .stage-item {
    background: var(--bg-color);
}

.theme-dark .task-item {
    background: var(--bg-color);
}

.theme-dark .stage-task-item {
    background: var(--bg-color);
}

/* 滚动条深色主题 */
.theme-dark ::-webkit-scrollbar-track {
    background: var(--bg-color);
}

.theme-dark ::-webkit-scrollbar-thumb {
    background: var(--border-color);
}

.theme-dark ::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* 代码高亮主题适配 */
.theme-dark .token.comment,
.theme-dark .token.prolog,
.theme-dark .token.doctype,
.theme-dark .token.cdata {
    color: #6a9955;
}

.theme-dark .token.punctuation {
    color: #d4d4d4;
}

.theme-dark .token.property,
.theme-dark .token.tag,
.theme-dark .token.boolean,
.theme-dark .token.number,
.theme-dark .token.constant,
.theme-dark .token.symbol,
.theme-dark .token.deleted {
    color: #b5cea8;
}

.theme-dark .token.selector,
.theme-dark .token.attr-name,
.theme-dark .token.string,
.theme-dark .token.char,
.theme-dark .token.builtin,
.theme-dark .token.inserted {
    color: #ce9178;
}

.theme-dark .token.operator,
.theme-dark .token.entity,
.theme-dark .token.url,
.theme-dark .language-css .token.string,
.theme-dark .style .token.string {
    color: #d4d4d4;
}

.theme-dark .token.atrule,
.theme-dark .token.attr-value,
.theme-dark .token.keyword {
    color: #569cd6;
}

.theme-dark .token.function,
.theme-dark .token.class-name {
    color: #dcdcaa;
}

.theme-dark .token.regex,
.theme-dark .token.important,
.theme-dark .token.variable {
    color: #d16969;
}

/* 图表主题适配 */
.theme-dark .chart-container canvas {
    filter: brightness(0.9);
}

/* 模态框深色主题 */
.theme-dark .modal {
    background: rgba(0, 0, 0, 0.7);
}

/* 表单元素深色主题 */
.theme-dark .form-control::placeholder {
    color: var(--text-secondary);
}

.theme-dark select.form-control option {
    background: var(--card-bg);
    color: var(--text-color);
}

/* 工具提示深色主题 */
.theme-dark .tooltip::before {
    background: var(--text-color);
    color: var(--card-bg);
}

/* 主题切换按钮样式 */
.theme-btn {
    position: relative;
    overflow: hidden;
}

.theme-btn i {
    transition: transform 0.3s ease;
}

.theme-dark .theme-btn i.fa-moon {
    transform: rotate(180deg);
}

.theme-light .theme-btn i.fa-sun {
    transform: rotate(0deg);
}

/* 专注模式样式 */
.focus-mode {
    --bg-color: #fafafa;
    --card-bg: #ffffff;
    --text-color: #2d3748;
    --text-secondary: #718096;
}

.theme-dark.focus-mode {
    --bg-color: #1a202c;
    --card-bg: #2d3748;
    --text-color: #f7fafc;
    --text-secondary: #a0aec0;
}

.focus-mode .sidebar,
.focus-mode .top-header {
    display: none !important;
}

.focus-mode .main-content {
    margin-left: 0 !important;
}

.focus-mode .page-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .theme-light {
        --border-color: #000000;
        --text-secondary: #000000;
    }
    
    .theme-dark {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .top-header,
    .btn,
    .modal {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .page-container {
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }
    
    .chart-container {
        display: none !important;
    }
}

/* 自定义主题变量 */
:root {
    --transition-duration: 0.3s;
    --border-radius: 0.375rem;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-base: 0.875rem;
    --line-height-base: 1.6;
    --spacing-unit: 1rem;
}

/* 主题切换动画效果 */
@keyframes themeSwitch {
    0% {
        opacity: 0.8;
        transform: scale(0.95);
    }
    50% {
        opacity: 0.9;
        transform: scale(1.02);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.theme-switching {
    animation: themeSwitch 0.3s ease-out;
}

/* 颜色盲友好调整 */
@media (prefers-color-scheme: no-preference) {
    .theme-light {
        --success-color: #059669;
        --warning-color: #d97706;
        --danger-color: #dc2626;
    }
    
    .theme-dark {
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
    }
}
