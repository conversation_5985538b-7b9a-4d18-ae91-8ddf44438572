<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Roo Code 学习管理系统</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/themes.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="theme-light">
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-code"></i>
                <span>Roo Code 学习</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <ul class="sidebar-menu">
            <li class="menu-item active" data-page="dashboard">
                <i class="fas fa-tachometer-alt"></i>
                <span>仪表板</span>
            </li>
            <li class="menu-item" data-page="learning-plan">
                <i class="fas fa-calendar-alt"></i>
                <span>学习计划</span>
            </li>
            <li class="menu-item" data-page="progress-tracking">
                <i class="fas fa-chart-line"></i>
                <span>进度跟踪</span>
            </li>
            <li class="menu-item" data-page="document-viewer">
                <i class="fas fa-file-alt"></i>
                <span>文档查看</span>
            </li>
            <li class="menu-item" data-page="practice-projects">
                <i class="fas fa-project-diagram"></i>
                <span>实践项目</span>
            </li>
            <li class="menu-item" data-page="settings">
                <i class="fas fa-cog"></i>
                <span>设置</span>
            </li>
        </ul>
        
        <div class="sidebar-footer">
            <div class="theme-toggle">
                <button id="themeToggle" class="theme-btn">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
            <div class="focus-mode">
                <button id="focusMode" class="focus-btn">
                    <i class="fas fa-eye"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <header class="top-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title" id="pageTitle">仪表板</h1>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">学习者</span>
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>
        </header>

        <!-- 页面内容容器 -->
        <div class="page-container" id="pageContainer">
            <!-- 仪表板页面 -->
            <div class="page-content active" id="dashboard-page">
                <div class="dashboard-grid">
                    <!-- 总体进度卡片 -->
                    <div class="card overall-progress">
                        <div class="card-header">
                            <h3>总体学习进度</h3>
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="card-body">
                            <div class="progress-circle" id="overallProgress">
                                <div class="progress-value">0%</div>
                            </div>
                            <div class="progress-stats">
                                <div class="stat">
                                    <span class="stat-label">已完成阶段</span>
                                    <span class="stat-value" id="completedStages">0/5</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-label">学习天数</span>
                                    <span class="stat-value" id="studyDays">0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 今日任务卡片 -->
                    <div class="card today-tasks">
                        <div class="card-header">
                            <h3>今日任务</h3>
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="card-body">
                            <div class="task-list" id="todayTaskList">
                                <!-- 动态生成任务列表 -->
                            </div>
                            <div class="task-summary">
                                <span>完成进度: <span id="todayProgress">0/0</span></span>
                            </div>
                        </div>
                    </div>

                    <!-- 学习统计卡片 -->
                    <div class="card study-stats">
                        <div class="card-header">
                            <h3>学习统计</h3>
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="card-body">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar-day"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-number" id="todayTime">0h</span>
                                        <span class="stat-label">今日学习</span>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar-week"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-number" id="weekTime">0h</span>
                                        <span class="stat-label">本周学习</span>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-number" id="totalTime">0h</span>
                                        <span class="stat-label">总计学习</span>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-fire"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-number" id="streak">0</span>
                                        <span class="stat-label">连续天数</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 技能雷达图 -->
                    <div class="card skill-radar">
                        <div class="card-header">
                            <h3>技能掌握度</h3>
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="skillRadarChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 学习趋势图 -->
                    <div class="card learning-trend">
                        <div class="card-header">
                            <h3>学习趋势</h3>
                            <i class="fas fa-chart-area"></i>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="learningTrendChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 阶段进度 -->
                    <div class="card stage-progress">
                        <div class="card-header">
                            <h3>阶段进度</h3>
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="card-body">
                            <div class="stage-list" id="stageProgressList">
                                <!-- 动态生成阶段进度 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 学习计划页面 -->
            <div class="page-content" id="learning-plan-page">
                <div class="learning-plan-container">
                    <div class="plan-header">
                        <h2>学习计划</h2>
                        <div class="plan-controls">
                            <button class="btn btn-primary" id="addTaskBtn">
                                <i class="fas fa-plus"></i> 添加任务
                            </button>
                            <button class="btn btn-secondary" id="resetPlanBtn">
                                <i class="fas fa-refresh"></i> 重置计划
                            </button>
                        </div>
                    </div>
                    
                    <div class="stages-container" id="stagesContainer">
                        <!-- 动态生成学习阶段 -->
                    </div>
                </div>
            </div>

            <!-- 进度跟踪页面 -->
            <div class="page-content" id="progress-tracking-page">
                <div class="progress-tracking-container">
                    <div class="tracking-header">
                        <h2>进度跟踪</h2>
                        <div class="tracking-controls">
                            <button class="btn btn-primary" id="addTimeBtn">
                                <i class="fas fa-clock"></i> 记录学习时间
                            </button>
                            <button class="btn btn-secondary" id="exportDataBtn">
                                <i class="fas fa-download"></i> 导出数据
                            </button>
                        </div>
                    </div>
                    
                    <div class="tracking-grid">
                        <div class="card time-tracking">
                            <div class="card-header">
                                <h3>时间记录</h3>
                            </div>
                            <div class="card-body">
                                <div class="time-input-form">
                                    <div class="form-group">
                                        <label>学习日期</label>
                                        <input type="date" id="studyDate" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label>学习时长（小时）</label>
                                        <input type="number" id="studyHours" class="form-control" min="0" max="24" step="0.5">
                                    </div>
                                    <div class="form-group">
                                        <label>学习内容</label>
                                        <textarea id="studyContent" class="form-control" rows="3"></textarea>
                                    </div>
                                    <button class="btn btn-primary" id="saveTimeRecord">保存记录</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card progress-charts">
                            <div class="card-header">
                                <h3>进度图表</h3>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="progressChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="time-records-table">
                        <h3>学习记录</h3>
                        <div class="table-container">
                            <table class="records-table" id="timeRecordsTable">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>时长</th>
                                        <th>内容</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="timeRecordsBody">
                                    <!-- 动态生成记录 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文档查看页面 -->
            <div class="page-content" id="document-viewer-page">
                <div class="document-viewer-container">
                    <div class="viewer-header">
                        <h2>文档查看</h2>
                        <div class="viewer-controls">
                            <select id="documentSelect" class="form-control">
                                <option value="">选择文档</option>
                                <option value="LEARNING_ROADMAP.md">学习路线图</option>
                                <option value="QUICK_START_GUIDE.md">快速开始指南</option>
                                <option value="LEARNING_PROGRESS_TRACKER.md">进度跟踪表</option>
                                <option value="PRACTICE_PROJECTS.md">实践项目</option>
                            </select>
                            <button class="btn btn-secondary" id="refreshDocBtn">
                                <i class="fas fa-refresh"></i> 刷新
                            </button>
                        </div>
                    </div>
                    
                    <div class="document-content" id="documentContent">
                        <div class="empty-state">
                            <i class="fas fa-file-alt"></i>
                            <p>请选择要查看的文档</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实践项目页面 -->
            <div class="page-content" id="practice-projects-page">
                <div class="projects-container">
                    <div class="projects-header">
                        <h2>实践项目</h2>
                        <div class="projects-controls">
                            <button class="btn btn-primary" id="addProjectBtn">
                                <i class="fas fa-plus"></i> 添加项目
                            </button>
                        </div>
                    </div>
                    
                    <div class="projects-grid" id="projectsGrid">
                        <!-- 动态生成项目卡片 -->
                    </div>
                </div>
            </div>

            <!-- 设置页面 -->
            <div class="page-content" id="settings-page">
                <div class="settings-container">
                    <div class="settings-header">
                        <h2>设置</h2>
                    </div>
                    
                    <div class="settings-sections">
                        <div class="settings-section">
                            <h3>数据管理</h3>
                            <div class="settings-group">
                                <button class="btn btn-primary" id="exportAllDataBtn">
                                    <i class="fas fa-download"></i> 导出所有数据
                                </button>
                                <button class="btn btn-secondary" id="importDataBtn">
                                    <i class="fas fa-upload"></i> 导入数据
                                </button>
                                <button class="btn btn-danger" id="clearAllDataBtn">
                                    <i class="fas fa-trash"></i> 清除所有数据
                                </button>
                            </div>
                            <input type="file" id="importFileInput" accept=".json" style="display: none;">
                        </div>
                        
                        <div class="settings-section">
                            <h3>个人偏好</h3>
                            <div class="settings-group">
                                <div class="setting-item">
                                    <label>每日学习目标（小时）</label>
                                    <input type="number" id="dailyGoal" class="form-control" min="0.5" max="12" step="0.5" value="2">
                                </div>
                                <div class="setting-item">
                                    <label>提醒设置</label>
                                    <div class="checkbox-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="enableReminders"> 启用学习提醒
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="enableNotifications"> 启用浏览器通知
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="settings-section">
                            <h3>关于</h3>
                            <div class="about-info">
                                <p><strong>Roo Code 学习管理系统</strong></p>
                                <p>版本: 1.0.0</p>
                                <p>基于 Roo Code 项目的系统性学习管理工具</p>
                                <p>帮助您高效掌握现代前端开发技能</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 模态框 -->
    <div class="modal" id="taskModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加任务</h3>
                <button class="modal-close" id="closeTaskModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="taskForm">
                    <div class="form-group">
                        <label>任务标题</label>
                        <input type="text" id="taskTitle" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label>所属阶段</label>
                        <select id="taskStage" class="form-control" required>
                            <option value="">选择阶段</option>
                            <option value="stage1">第一阶段：项目结构和基础架构</option>
                            <option value="stage2">第二阶段：核心架构模式</option>
                            <option value="stage3">第三阶段：TypeScript类型安全</option>
                            <option value="stage4">第四阶段：前端组件状态管理</option>
                            <option value="stage5">第五阶段：算法性能优化</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>任务描述</label>
                        <textarea id="taskDescription" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>预计时长（小时）</label>
                        <input type="number" id="taskDuration" class="form-control" min="0.5" max="8" step="0.5">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelTask">取消</button>
                <button class="btn btn-primary" id="saveTask">保存</button>
            </div>
        </div>
    </div>

    <!-- JavaScript 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/5.1.1/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <!-- 应用脚本 -->
    <script src="js/app.js"></script>
    <script src="js/data-manager.js"></script>
    <script src="js/chart-manager.js"></script>
    <script src="js/document-viewer.js"></script>
    <script src="js/progress-tracker.js"></script>
</body>
</html>
